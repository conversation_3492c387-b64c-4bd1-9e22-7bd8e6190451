using System.ComponentModel.DataAnnotations;

namespace Application.Contracts.DTOs;

public class CustomerDto
{
    public Guid Id { get; set; }
    public string NameSurname { get; set; } = null!;
    public string? PhoneNumber { get; set; }
    public string? Email { get; set; }
    public string? TaxOrIdentityNumber { get; set; }
    public string? TaxOffice { get; set; }
    public bool IsActive { get; set; }
    public bool AcceptedKvkk { get; set; }
    public bool AcceptedPrivacyPolicy { get; set; }
    public DateTime? KvkkAcceptedAt { get; set; }
    public DateTime? PrivacyPolicyAcceptedAt { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
    
    // Navigation properties
    public int AddressCount { get; set; }
    public int OrderCount { get; set; }
    public decimal TotalOrderAmount { get; set; }
    public decimal PointBalance { get; set; }
    public int CouponCount { get; set; }
}

public class CustomerListDto
{
    public Guid Id { get; set; }
    public string NameSurname { get; set; } = null!;
    public string? Email { get; set; }
    public string? PhoneNumber { get; set; }
    public bool IsActive { get; set; }
    public DateTime CreatedAt { get; set; }
    public int OrderCount { get; set; }
    public decimal TotalOrderAmount { get; set; }
    public DateTime? LastOrderDate { get; set; }
}

public class CustomerCreateDto
{
    [Required]
    [StringLength(200, MinimumLength = 2)]
    public string NameSurname { get; set; } = null!;

    [Required]
    [StringLength(100, MinimumLength = 6)]
    public string Password { get; set; } = null!;

    [EmailAddress]
    [StringLength(200)]
    public string? Email { get; set; }

    [Phone]
    [StringLength(20)]
    public string? PhoneNumber { get; set; }

    [StringLength(50)]
    public string? TaxOrIdentityNumber { get; set; }

    [StringLength(200)]
    public string? TaxOffice { get; set; }

    [Required]
    public bool AcceptedKvkk { get; set; } = false;

    [Required]
    public bool AcceptedPrivacyPolicy { get; set; } = false;

    public bool IsActive { get; set; } = true;
}

public class CustomerUpdateDto
{
    [Required]
    public Guid Id { get; set; }

    [Required]
    [StringLength(200, MinimumLength = 2)]
    public string NameSurname { get; set; } = null!;

    [EmailAddress]
    [StringLength(200)]
    public string? Email { get; set; }

    [Phone]
    [StringLength(20)]
    public string? PhoneNumber { get; set; }

    [StringLength(50)]
    public string? TaxOrIdentityNumber { get; set; }

    [StringLength(200)]
    public string? TaxOffice { get; set; }

    public bool IsActive { get; set; }
}

public class CustomerPasswordChangeDto
{
    [Required]
    public Guid CustomerId { get; set; }

    [Required]
    [StringLength(100, MinimumLength = 6)]
    public string NewPassword { get; set; } = null!;
}

public class CustomerPasswordVerifyDto
{
    [Required]
    public Guid CustomerId { get; set; }

    [Required]
    public string Password { get; set; } = null!;
}

public class CustomerSearchDto
{
    public string? SearchTerm { get; set; }
    public string? Email { get; set; }
    public string? PhoneNumber { get; set; }
    public bool? IsActive { get; set; }
    public DateTime? CreatedAfter { get; set; }
    public DateTime? CreatedBefore { get; set; }
    public decimal? MinOrderAmount { get; set; }
    public decimal? MaxOrderAmount { get; set; }
    public int? Page { get; set; } = 1;
    public int? PageSize { get; set; } = 20;
    public string? SortBy { get; set; } = "CreatedAt";
    public string? SortDirection { get; set; } = "desc";
}

public class CustomerAnalyticsDto
{
    public int TotalCustomers { get; set; }
    public int ActiveCustomers { get; set; }
    public int InactiveCustomers { get; set; }
    public int NewCustomersThisMonth { get; set; }
    public int NewCustomersLastMonth { get; set; }
    public decimal AverageOrderAmount { get; set; }
    public decimal TotalCustomerValue { get; set; }
    public List<CustomerRegistrationByMonthDto> RegistrationsByMonth { get; set; } = [];
    public List<CustomerTopSpendersDto> TopSpenders { get; set; } = [];
}

public class CustomerRegistrationByMonthDto
{
    public int Year { get; set; }
    public int Month { get; set; }
    public string MonthName { get; set; } = null!;
    public int Count { get; set; }
}

public class CustomerTopSpendersDto
{
    public Guid CustomerId { get; set; }
    public string CustomerName { get; set; } = null!;
    public string? Email { get; set; }
    public decimal TotalSpent { get; set; }
    public int OrderCount { get; set; }
}

public class CustomerDetailDto
{
    public Guid Id { get; set; }
    public string NameSurname { get; set; } = null!;
    public string? PhoneNumber { get; set; }
    public string? Email { get; set; }
    public string? TaxOrIdentityNumber { get; set; }
    public string? TaxOffice { get; set; }
    public bool IsActive { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }

    // Statistics
    public int AddressCount { get; set; }
    public int OrderCount { get; set; }
    public decimal TotalOrderAmount { get; set; }
    public decimal PointBalance { get; set; }
    public int CouponCount { get; set; }
    public int ReviewCount { get; set; }
    public int FavouriteCount { get; set; }
    public DateTime? LastOrderDate { get; set; }
    public DateTime? LastLoginDate { get; set; }

    // Related data
    public List<AddressDto> Addresses { get; set; } = [];
    public List<OrderListDto> RecentOrders { get; set; } = [];
    public List<UserPointListDto> RecentPoints { get; set; } = [];
    public List<CouponListDto> ActiveCoupons { get; set; } = [];
}

// Customer Authentication DTOs
public class CustomerLoginDto
{
    [Required]
    [EmailAddress]
    public string Email { get; set; } = null!;

    [Required]
    [StringLength(100, MinimumLength = 6)]
    public string Password { get; set; } = null!;
}

public class CustomerRegisterDto
{
    [Required]
    [StringLength(200, MinimumLength = 2)]
    public string NameSurname { get; set; } = null!;

    [Required]
    [EmailAddress]
    [StringLength(200)]
    public string Email { get; set; } = null!;

    [Required]
    [StringLength(100, MinimumLength = 6)]
    public string Password { get; set; } = null!;

    [Phone]
    [StringLength(20)]
    public string? PhoneNumber { get; set; }

    [StringLength(50)]
    public string? TaxOrIdentityNumber { get; set; }

    [StringLength(200)]
    public string? TaxOffice { get; set; }

    [Required]
    public bool AcceptedKvkk { get; set; } = false;

    [Required]
    public bool AcceptedPrivacyPolicy { get; set; } = false;
}

public class CustomerAuthResponseDto
{
    public bool IsSuccessful { get; set; }
    public string Message { get; set; } = string.Empty;
    public string Token { get; set; } = string.Empty;
    public CustomerDto? Customer { get; set; }
}


