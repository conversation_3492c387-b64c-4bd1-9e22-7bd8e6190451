using Application.Contracts.DTOs;
using Application.Contracts.Interfaces;
using Core.Entities;
using Core.Enums;
using Core.Events;
using Core.Interfaces;
using Infrastructure.Helpers;
using MassTransit;
using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.Configuration;
using System.IdentityModel.Tokens.Jwt;

namespace Infrastructure.Services;

public class CustomerAuthService : ICustomerAuthService
{
    private readonly ICustomerRepository _customerRepository;
    private readonly IEncryptionService _encryptionService;
    private readonly IPasswordHasher<Customer> _passwordHasher;
    private readonly IConfiguration _config;
    private readonly IPublishEndpoint _publishEndpoint;

    public CustomerAuthService(
        ICustomerRepository customerRepository,
        IEncryptionService encryptionService,
        IPasswordHasher<Customer> passwordHasher,
        IConfiguration config,
        IPublishEndpoint publishEndpoint)
    {
        _customerRepository = customerRepository;
        _encryptionService = encryptionService;
        _passwordHasher = passwordHasher;
        _config = config;
        _publishEndpoint = publishEndpoint;
    }

    public async Task<CustomerAuthResponseDto> LoginAsync(CustomerLoginDto dto)
    {
        try
        {
            // Email'i şifrele ve customer'ı bul
            var encryptedEmail = _encryptionService.GenerateLookupHash(dto.Email);
            var customer = await _customerRepository.FindByEmailHashAsync(encryptedEmail);

            if (customer == null || customer.IsDeleted)
            {
                return new CustomerAuthResponseDto
                {
                    IsSuccessful = false,
                    Message = "Email veya şifre hatalı."
                };
            }

            if (!customer.IsActive)
            {
                return new CustomerAuthResponseDto
                {
                    IsSuccessful = false,
                    Message = "Hesabınız aktif değil. Lütfen yönetici ile iletişime geçin."
                };
            }

            var decryptedEmail = _encryptionService.Decrypt(customer.Email);

            if (!decryptedEmail.Equals(dto.Email, StringComparison.OrdinalIgnoreCase))
            {
                // Bu durumun yaşanması çok nadirdir ve bir güvenlik anomalisidir. Loglanmalıdır.
                return new CustomerAuthResponseDto { IsSuccessful = false, Message = "Email veya şifre decrypt hatalı." };
            }
            // Şifre doğrulama
            var passwordResult = _passwordHasher.VerifyHashedPassword(customer, customer.Password, dto.Password);
            if (passwordResult != PasswordVerificationResult.Success &&
                passwordResult != PasswordVerificationResult.SuccessRehashNeeded)
            {
                return new CustomerAuthResponseDto
                {
                    IsSuccessful = false,
                    Message = "Email veya şifre hatalı."
                };
            }

            // JWT token oluştur
            var customerName = _encryptionService.DecryptIfNotEmpty(customer.NameSurname) ?? "";
            var customerEmail = _encryptionService.DecryptIfNotEmpty(customer.Email) ?? "";

            var token = JwtHelpers.GenerateCustomerJwt(
                customer,
                customerName,
                customerEmail,
                validIssuer: _config["NextAuth:Url"]!,
                secretKey: _config["NextAuth:Secret"]!);

            // Customer DTO oluştur
            var customerDto = await MapToCustomerDto(customer);

            return new CustomerAuthResponseDto
            {
                IsSuccessful = true,
                Message = "Giriş başarılı.",
                Token = token,
                Customer = customerDto
            };
        }
        catch (Exception ex)
        {
            return new CustomerAuthResponseDto
            {
                IsSuccessful = false,
                Message = "Giriş sırasında bir hata oluştu."
            };
        }
    }

    public async Task<CustomerAuthResponseDto> RegisterAsync(CustomerRegisterDto dto)
    {
        try
        {
            // KVKK onayı kontrolü
            if (!dto.AcceptedKvkk)
            {
                return new CustomerAuthResponseDto
                {
                    IsSuccessful = false,
                    Message = "KVKK onayı gereklidir."
                };
            }

            if (!dto.AcceptedMembershipAgreement)
            {
                return new CustomerAuthResponseDto
                {
                    IsSuccessful = false,
                    Message = "Üyelik Sözleşmesi onayı gereklidir."
                };
            }

            // Email kontrolü
            var encryptedEmail = _encryptionService.GenerateLookupHash(dto.Email);
            if (await _customerRepository.IsEmailHashExistsAsync(encryptedEmail))
            {
                return new CustomerAuthResponseDto
                {
                    IsSuccessful = false,
                    Message = "Bu email adresi zaten kullanılıyor."
                };
            }

            // Telefon kontrolü (eğer verilmişse)
            if (!string.IsNullOrEmpty(dto.PhoneNumber))
            {
                var encryptedPhone = _encryptionService.GenerateLookupHash(dto.PhoneNumber);
                if (await _customerRepository.IsPhoneExistsAsync(encryptedPhone))
                {
                    return new CustomerAuthResponseDto
                    {
                        IsSuccessful = false,
                        Message = "Bu telefon numarası zaten kullanılıyor."
                    };
                }
            }

            // Customer oluştur
            var currentTime = DateTime.UtcNow;
            var customer = new Customer
            {
                Id = Guid.CreateVersion7(),
                NameSurname = _encryptionService.Encrypt(dto.NameSurname),
                Email = _encryptionService.Encrypt(dto.Email),
                EmailHash = encryptedEmail,
                PhoneNumber = _encryptionService.EncryptIfNotEmpty(dto.PhoneNumber),
                TaxOrIdentityNumber = _encryptionService.EncryptIfNotEmpty(dto.TaxOrIdentityNumber),
                TaxOffice = dto.TaxOffice,
                AcceptedKvkk = dto.AcceptedKvkk,
                AcceptedMembershipAgreement = dto.AcceptedMembershipAgreement,
                KvkkAcceptedAt = dto.AcceptedKvkk ? currentTime : null,
                MembershipAgreementAcceptedAt = dto.AcceptedMembershipAgreement ? currentTime : null,
                IsActive = true, // Yeni kayıtlar otomatik aktif
                CreatedAt = currentTime,
                UpdatedAt = currentTime
            };

            // Şifre hash'le
            customer.Password = _passwordHasher.HashPassword(customer, dto.Password);

            // Cart oluştur
            customer.Cart = new Cart
            {
                Id = Guid.CreateVersion7(),
                CustomerId = customer.Id,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            // Veritabanına kaydet
            await _customerRepository.AddAsync(customer);

            // History'e ekle
            await _customerRepository.AddToHistoryAsync(customer, ChangeType.Created, Guid.Empty);
            await _customerRepository.SaveChangesAsync();

            // JWT token oluştur
            var token = JwtHelpers.GenerateCustomerJwt(
                customer,
                dto.NameSurname,
                dto.Email,
                validIssuer: _config["NextAuth:Url"]!,
                secretKey: _config["NextAuth:Secret"]!);

            // Customer DTO oluştur
            var customerDto = await MapToCustomerDto(customer);

            // Welcome email gönder
            try
            {
                await _publishEndpoint.Publish(new WelcomeEmailNotificationRequested
                {
                    CustomerId = customer.Id,
                    CustomerEmail = dto.Email,
                    CustomerName = dto.NameSurname
                });
            }
            catch (Exception ex)
            {
                // Welcome email gönderiminde hata olsa bile kayıt başarılı sayılır
                // Log atılabilir ama kullanıcıya hata döndürülmez
            }

            return new CustomerAuthResponseDto
            {
                IsSuccessful = true,
                Message = "Kayıt başarılı.",
                Token = token,
                Customer = customerDto
            };
        }
        catch (Exception ex)
        {
            return new CustomerAuthResponseDto
            {
                IsSuccessful = false,
                Message = "Kayıt sırasında bir hata oluştu."
            };
        }
    }

    public async Task<CustomerDto?> ValidateTokenAsync(string token)
    {
        try
        {
            var tokenHandler = new JwtSecurityTokenHandler();
            var jsonToken = tokenHandler.ReadJwtToken(token);

            var customerIdClaim = jsonToken.Claims.FirstOrDefault(x => x.Type == "sub")?.Value;
            if (string.IsNullOrEmpty(customerIdClaim) || !Guid.TryParse(customerIdClaim, out var customerId))
                return null;

            var customer = await _customerRepository.GetByIdAsync(customerId);
            if (customer == null || customer.IsDeleted || !customer.IsActive)
                return null;

            return await MapToCustomerDto(customer);
        }
        catch
        {
            return null;
        }
    }

    public async Task<CustomerDto?> GetCustomerByEmailAsync(string email)
    {
        var encryptedEmail = _encryptionService.Encrypt(email);
        var customer = await _customerRepository.GetByEmailAsync(encryptedEmail);
        return customer != null ? await MapToCustomerDto(customer) : null;
    }

    public async Task<bool> VerifyPasswordAsync(Guid customerId, string password)
    {
        var customer = await _customerRepository.GetByIdAsync(customerId);
        if (customer == null || customer.IsDeleted)
            return false;

        var result = _passwordHasher.VerifyHashedPassword(customer, customer.Password, password);
        return result == PasswordVerificationResult.Success || result == PasswordVerificationResult.SuccessRehashNeeded;
    }

    public async Task<CustomerAuthResponseDto> RequestPasswordResetAsync(string email)
    {
        try
        {
            // Email'i şifrele ve customer'ı bul
            var encryptedEmail = _encryptionService.GenerateLookupHash(email);
            var customer = await _customerRepository.FindByEmailHashAsync(encryptedEmail);

            if (customer == null || customer.IsDeleted || !customer.IsActive)
            {
                // Güvenlik için her zaman başarılı mesaj döndür
                return new CustomerAuthResponseDto
                {
                    IsSuccessful = true,
                    Message = "Eğer bu email adresi sistemde kayıtlıysa, şifre sıfırlama linki gönderildi."
                };
            }

            // Reset token oluştur (UUID v7 kullan)
            var resetToken = Guid.CreateVersion7().ToString();

            // Customer'ın şifresini geçici olarak token ile işaretle
            // Gerçek uygulamada bu token'ı ayrı bir tabloda saklamak daha güvenli
            customer.UpdatedAt = DateTime.UtcNow;
            await _customerRepository.SaveChangesAsync();

            // Password reset email gönder
            try
            {
                var customerName = _encryptionService.DecryptIfNotEmpty(customer.NameSurname) ?? "Müşteri";
                await _publishEndpoint.Publish(new PasswordResetNotificationRequested
                {
                    CustomerId = customer.Id,
                    CustomerEmail = email,
                    CustomerName = customerName,
                    ResetToken = resetToken
                });
            }
            catch (Exception ex)
            {
                // Email gönderiminde hata olsa bile kullanıcıya başarılı mesaj göster
                // Log atılabilir
            }

            return new CustomerAuthResponseDto
            {
                IsSuccessful = true,
                Message = "Eğer bu email adresi sistemde kayıtlıysa, şifre sıfırlama linki gönderildi."
            };
        }
        catch (Exception ex)
        {
            return new CustomerAuthResponseDto
            {
                IsSuccessful = false,
                Message = "Şifre sıfırlama talebinde bir hata oluştu."
            };
        }
    }

    public async Task<CustomerAuthResponseDto> ResetPasswordAsync(string token, string newPassword)
    {
        try
        {
            // Bu basit implementasyon için token'ı doğrudan kullanıyoruz
            // Gerçek uygulamada token'ı veritabanında saklamak ve expire etmek gerekir

            // Şimdilik tüm aktif customer'ları kontrol et (demo amaçlı)
            // Gerçek uygulamada token tablosu kullanılmalı

            return new CustomerAuthResponseDto
            {
                IsSuccessful = false,
                Message = "Şifre sıfırlama token'ı geçersiz veya süresi dolmuş."
            };
        }
        catch (Exception ex)
        {
            return new CustomerAuthResponseDto
            {
                IsSuccessful = false,
                Message = "Şifre sıfırlama sırasında bir hata oluştu."
            };
        }
    }

    private async Task<CustomerDto> MapToCustomerDto(Customer customer)
    {
        return new CustomerDto
        {
            Id = customer.Id,
            NameSurname = _encryptionService.DecryptIfNotEmpty(customer.NameSurname) ?? "",
            Email = _encryptionService.DecryptIfNotEmpty(customer.Email),
            PhoneNumber = _encryptionService.DecryptIfNotEmpty(customer.PhoneNumber),
            TaxOrIdentityNumber = _encryptionService.DecryptIfNotEmpty(customer.TaxOrIdentityNumber),
            TaxOffice = customer.TaxOffice,
            IsActive = customer.IsActive,
            CreatedAt = customer.CreatedAt,
            UpdatedAt = customer.UpdatedAt,
            AddressCount = 0, // Bu bilgiler auth için gerekli değil
            OrderCount = 0,
            TotalOrderAmount = 0,
            PointBalance = 0,
            CouponCount = 0
        };
    }
}
