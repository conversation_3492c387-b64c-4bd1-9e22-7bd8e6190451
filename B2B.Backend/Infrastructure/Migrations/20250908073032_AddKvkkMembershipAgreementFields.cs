﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class AddKvkkMembershipAgreementFields : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "PrivacyPolicyAcceptedAt",
                table: "CustomersHistory",
                newName: "MembershipAgreementAcceptedAt");

            migrationBuilder.RenameColumn(
                name: "AcceptedPrivacyPolicy",
                table: "CustomersHistory",
                newName: "AcceptedMembershipAgreement");

            migrationBuilder.RenameColumn(
                name: "PrivacyPolicyAcceptedAt",
                table: "Customers",
                newName: "MembershipAgreementAcceptedAt");

            migrationBuilder.RenameColumn(
                name: "AcceptedPrivacyPolicy",
                table: "Customers",
                newName: "AcceptedMembershipAgreement");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "MembershipAgreementAcceptedAt",
                table: "CustomersHistory",
                newName: "PrivacyPolicyAcceptedAt");

            migrationBuilder.RenameColumn(
                name: "AcceptedMembershipAgreement",
                table: "CustomersHistory",
                newName: "AcceptedPrivacyPolicy");

            migrationBuilder.RenameColumn(
                name: "MembershipAgreementAcceptedAt",
                table: "Customers",
                newName: "PrivacyPolicyAcceptedAt");

            migrationBuilder.RenameColumn(
                name: "AcceptedMembershipAgreement",
                table: "Customers",
                newName: "AcceptedPrivacyPolicy");
        }
    }
}
